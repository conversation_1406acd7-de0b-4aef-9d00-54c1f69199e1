{"name": "vest-portal", "author": "tenset.io", "version": "0.0.0", "type": "module", "scripts": {"cz": "cz", "prepare": "husky install", "dev": "vite", "build": "tsc && vite build", "deploy": "vercel deploy dist  --archive=tgz --prod", "serve": "vite preview", "lint:fix": "eslint ./src --ext .jsx,.js,.ts,.tsx --quiet --fix --ignore-path ./.gitignore", "lint:format": "prettier --log-level warn --write \"./**/*.{js,jsx,ts,tsx,css,md}\" ", "lint": "npm run lint:format && npm run lint:fix ", "type-check": "tsc"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "class-variance-authority": "^0.7.0", "clsx": "2.1.0", "lucide-react": "^0.344.0", "react": "18.2.0", "react-countdown": "^2.3.5", "react-dom": "18.2.0", "react-hook-form": "^7.51.1", "react-router-dom": "^6.22.0", "tailwind-merge": "2.2.1", "tailwindcss-animate": "^1.0.7", "timemachine": "^0.3.2", "viem": "^2.9.2", "wagmi": "^2.5.12", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.11.20", "@types/react": "^18.2.60", "@types/react-dom": "^18.2.19", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.33.2", "eslint-plugin-simple-import-sort": "^12.0.0", "husky": "^9.0.11", "postcss": "^8.4.35", "pre-commit": "^1.2.2", "prettier": "^3.2.5", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "vite": "^5.1.4"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}