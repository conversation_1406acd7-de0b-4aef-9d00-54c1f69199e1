import { Link, useLocation } from 'react-router-dom';
import { useAccount } from 'wagmi';
import { Button } from './ui/button';

export default function AdminNavigation() {
  const { address } = useAccount();
  const location = useLocation();

  // Check if user is admin (you can implement your own admin check logic)
  const isAdmin = address && (
    address.toLowerCase() === '0x979f05722488a1d134c109dcf77a9af584998825' || // Admin address
    process.env.NODE_ENV === 'development' // Allow in development
  );

  if (!isAdmin) {
    return null;
  }

  const isAdminPage = location.pathname === '/admin';

  return (
    <div className="fixed top-4 right-4 z-50">
      <Link to={isAdminPage ? '/' : '/admin'}>
        <Button
          variant={isAdminPage ? 'default' : 'outline'}
          size="sm"
          className="bg-purple-600 hover:bg-purple-700 border-purple-500"
        >
          {isAdminPage ? 'Back to App' : 'Admin Panel'}
        </Button>
      </Link>
    </div>
  );
}