import { useContext, useEffect, useState } from 'react';
import { useAccount } from 'wagmi';
import { formatUnits, isAddress, Address } from 'viem';

import { Membership } from '@/class/interface/presale';
import { MembershipsContext } from '@/providers/membership-provider';
import { OldMembershipsContext } from '@/providers/old-membership-provider';
import { PresaleContext } from '@/providers/presale-provider';
import { ProjectConfigContext } from '@/providers/project-config-provider';
import { PublicClientContext } from '@/providers/public-client-provider';
import { TokensContext } from '@/providers/tokens-provider';
import { cutDecimals } from '@/lib/cut-decimals';

import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Spinner } from './icons/spinner';
import { Input } from './ui/input';
import { Label } from './ui/label';

interface AdminStats {
  totalOldMemberships: number;
  totalNewMemberships: number;
  totalOldFunds: bigint;
  totalNewFunds: bigint;
  totalMigrationCandidates: number;
  connectedUsers: number;
}

interface UserLookupData {
  address: Address;
  oldMemberships: Membership[];
  newMemberships: Membership[];
  loading: boolean;
  error: string | null;
}

export default function AdminPage() {
  const { address, isConnected } = useAccount();
  const { memberships: newMemberships, loading: newLoading } = useContext(MembershipsContext);
  const { memberships: oldMemberships, loading: oldLoading } = useContext(OldMembershipsContext);
  const { presaleInstance, presaleData, selectedRoundId } = useContext(PresaleContext);
  const { chain, vestedToken, collectedToken } = useContext(ProjectConfigContext);
  const { vestedToken: vestedTokenData, collectedToken: collectedTokenData } = useContext(TokensContext);
  const { publicClient } = useContext(PublicClientContext);

  const [adminStats, setAdminStats] = useState<AdminStats>({
    totalOldMemberships: 0,
    totalNewMemberships: 0,
    totalOldFunds: 0n,
    totalNewFunds: 0n,
    totalMigrationCandidates: 0,
    connectedUsers: 0,
  });

  const [expandedSection, setExpandedSection] = useState<string | null>(null);
  const [userLookupAddress, setUserLookupAddress] = useState<string>('');
  const [userLookupData, setUserLookupData] = useState<UserLookupData | null>(null);
  const [isLookingUp, setIsLookingUp] = useState(false);

  // Check if user is admin (you can implement your own admin check logic)
  const isAdmin = address && (
    address.toLowerCase() === '0x...' || // Add admin addresses here
    process.env.NODE_ENV === 'development' // Allow in development
  );

  useEffect(() => {
    if (!newLoading && !oldLoading) {
      calculateStats();
    }
  }, [newMemberships, oldMemberships, newLoading, oldLoading]);

  const calculateStats = () => {
    // Calculate total funds in old memberships
    const totalOldFunds = oldMemberships.reduce((sum, membership) => {
      const claimableAmount = BigInt(membership.usage.max) - BigInt(membership.usage.current);
      return sum + claimableAmount;
    }, 0n);

    // Calculate total funds in new memberships
    const totalNewFunds = newMemberships.reduce((sum, membership) => {
      return sum + BigInt(membership.usage.current);
    }, 0n);

    // Count migration candidates (old memberships with claimable funds)
    const migrationCandidates = oldMemberships.filter(membership => {
      const claimableAmount = BigInt(membership.usage.max) - BigInt(membership.usage.current);
      return claimableAmount > 0n;
    }).length;

    setAdminStats({
      totalOldMemberships: oldMemberships.length,
      totalNewMemberships: newMemberships.length,
      totalOldFunds,
      totalNewFunds,
      totalMigrationCandidates: migrationCandidates,
      connectedUsers: isConnected ? 1 : 0, // This would need to be tracked differently in a real app
    });
  };

  const toggleSection = (section: string) => {
    setExpandedSection(expandedSection === section ? null : section);
  };

  const formatTokenAmount = (amount: bigint, decimals: number, symbol: string) => {
    return `${cutDecimals(formatUnits(amount, decimals), 4)} ${symbol}`;
  };

  // Handle user lookup
  const handleUserLookup = async () => {
    if (!userLookupAddress.trim()) {
      return;
    }

    if (!isAddress(userLookupAddress)) {
      setUserLookupData({
        address: userLookupAddress as Address,
        oldMemberships: [],
        newMemberships: [],
        loading: false,
        error: 'Invalid Ethereum address format'
      });
      return;
    }

    setIsLookingUp(true);
    setUserLookupData({
      address: userLookupAddress as Address,
      oldMemberships: [],
      newMemberships: [],
      loading: true,
      error: null
    });

    try {
      // Fetch old memberships
      let userOldMemberships: Membership[] = [];
      if (presaleInstance) {
        try {
          // Create old presale instance to fetch old memberships
          const { PresaleFactory } = await import('@/class/presale-factory');
          const { PublicClientContext } = await import('@/providers/public-client-provider');
          const oldPresaleInstance = await PresaleFactory.createInstance(
            'v3', // or 'v4' based on old contract version
            publicClient,
            '******************************************' as Address
          );
          userOldMemberships = await oldPresaleInstance.getMemberships(userLookupAddress as Address);
        } catch (error) {
          console.error('Error fetching old memberships for user:', error);
        }
      }

      // Fetch new memberships
      let userNewMemberships: Membership[] = [];
      if (presaleInstance) {
        try {
          userNewMemberships = await presaleInstance.getMemberships(userLookupAddress as Address);
        } catch (error) {
          console.error('Error fetching new memberships for user:', error);
        }
      }

      setUserLookupData({
        address: userLookupAddress as Address,
        oldMemberships: userOldMemberships,
        newMemberships: userNewMemberships,
        loading: false,
        error: null
      });
    } catch (error) {
      console.error('User lookup failed:', error);
      setUserLookupData({
        address: userLookupAddress as Address,
        oldMemberships: [],
        newMemberships: [],
        loading: false,
        error: 'Failed to fetch user data. Please try again.'
      });
    } finally {
      setIsLookingUp(false);
    }
  };

  const clearUserLookup = () => {
    setUserLookupAddress('');
    setUserLookupData(null);
  };

  if (!isAdmin) {
    return (
      <div className="container mx-auto p-6">
        <Card className="bg-red-900/20 border-red-700">
          <CardHeader>
            <CardTitle className="text-red-400">Access Denied</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-300">You don't have permission to access the admin panel.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (newLoading || oldLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center py-12">
          <Spinner className="animate-spin w-8 h-8 mr-3" />
          <span className="text-gray-300">Loading admin data...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-white">Admin Dashboard</h1>
        <div className="text-sm text-gray-400">
          Connected as: {address ? `${address.slice(0, 6)}...${address.slice(-4)}` : 'Not connected'}
        </div>
      </div>

      {/* User Lookup Section */}
      <Card className="bg-purple-900/20 border-purple-700">
        <CardHeader>
          <CardTitle className="text-purple-400">User Lookup</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <div className="flex-1">
              <Label htmlFor="userAddress" className="text-sm text-gray-300">
                Enter User Address
              </Label>
              <Input
                id="userAddress"
                type="text"
                placeholder="0x..."
                value={userLookupAddress}
                onChange={(e) => setUserLookupAddress(e.target.value)}
                className="mt-1"
                disabled={isLookingUp}
              />
            </div>
            <div className="flex items-end gap-2">
              <Button
                onClick={handleUserLookup}
                disabled={isLookingUp || !userLookupAddress.trim()}
                className="bg-purple-600 hover:bg-purple-700"
              >
                {isLookingUp ? (
                  <>
                    <Spinner className="animate-spin w-4 h-4 mr-2" />
                    Looking up...
                  </>
                ) : (
                  'Lookup'
                )}
              </Button>
              {userLookupData && (
                <Button
                  variant="outline"
                  onClick={clearUserLookup}
                  disabled={isLookingUp}
                >
                  Clear
                </Button>
              )}
            </div>
          </div>

          {userLookupData && (
            <div className="mt-4 p-4 bg-gray-800/50 rounded-lg border border-gray-700">
              <h4 className="text-sm font-medium text-gray-300 mb-3">
                Results for: {userLookupData.address.slice(0, 6)}...{userLookupData.address.slice(-4)}
              </h4>
              
              {userLookupData.error ? (
                <div className="text-red-400 text-sm">{userLookupData.error}</div>
              ) : userLookupData.loading ? (
                <div className="flex items-center gap-2 text-gray-400">
                  <Spinner className="animate-spin w-4 h-4" />
                  <span>Loading user data...</span>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h5 className="text-xs font-medium text-blue-400 mb-2">Old Contract</h5>
                    <div className="text-sm text-gray-300">
                      <div>Memberships: {userLookupData.oldMemberships.length}</div>
                      <div>
                        Total Claimable: {formatTokenAmount(
                          userLookupData.oldMemberships.reduce((sum, m) => 
                            sum + (BigInt(m.usage.max) - BigInt(m.usage.current)), 0n
                          ),
                          vestedToken.decimals,
                          vestedToken.symbol
                        )}
                      </div>
                    </div>
                  </div>
                  <div>
                    <h5 className="text-xs font-medium text-green-400 mb-2">New Contract</h5>
                    <div className="text-sm text-gray-300">
                      <div>Memberships: {userLookupData.newMemberships.length}</div>
                      <div>
                        Total Invested: {formatTokenAmount(
                          userLookupData.newMemberships.reduce((sum, m) => 
                            sum + BigInt(m.usage.current), 0n
                          ),
                          vestedToken.decimals,
                          vestedToken.symbol
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card className="bg-blue-900/20 border-blue-700">
          <CardHeader className="pb-2">
            <CardTitle className="text-blue-400 text-sm">Old Contract Memberships</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{adminStats.totalOldMemberships}</div>
            <div className="text-xs text-gray-400">
              Total funds: {formatTokenAmount(adminStats.totalOldFunds, vestedToken.decimals, vestedToken.symbol)}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-green-900/20 border-green-700">
          <CardHeader className="pb-2">
            <CardTitle className="text-green-400 text-sm">New Contract Memberships</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{adminStats.totalNewMemberships}</div>
            <div className="text-xs text-gray-400">
              Total invested: {formatTokenAmount(adminStats.totalNewFunds, vestedToken.decimals, vestedToken.symbol)}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-yellow-900/20 border-yellow-700">
          <CardHeader className="pb-2">
            <CardTitle className="text-yellow-400 text-sm">Migration Candidates</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{adminStats.totalMigrationCandidates}</div>
            <div className="text-xs text-gray-400">
              Memberships with claimable funds
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Contract Information */}
      <Card className="bg-gray-900/50 border-gray-700">
        <CardHeader>
          <CardTitle className="text-white">Contract Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium text-gray-300 mb-2">Current Presale Contract</h4>
              <div className="text-xs text-gray-400 space-y-1">
                <div>Address: {presaleInstance?.getPresaleData().presaleContractAddress || 'Not loaded'}</div>
                <div>Version: {presaleInstance?.getVersion() || 'Unknown'}</div>
                <div>Active Round: {selectedRoundId}</div>
              </div>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-300 mb-2">Token Information</h4>
              <div className="text-xs text-gray-400 space-y-1">
                <div>Vested Token: {vestedToken.symbol} ({vestedToken.address})</div>
                <div>Collected Token: {collectedToken.symbol} ({collectedToken.address})</div>
                <div>Chain: {chain.name} (ID: {chain.id})</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Old Memberships Details */}
      <Card className="bg-gray-900/50 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="text-white">Old Contract Memberships</span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => toggleSection('old')}
            >
              {expandedSection === 'old' ? 'Collapse' : 'Expand'}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {expandedSection === 'old' && (
            <div className="space-y-4">
              {oldMemberships.length === 0 ? (
                <p className="text-gray-400">No old memberships found.</p>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-gray-700">
                        <th className="text-left py-2 text-gray-300">ID</th>
                        <th className="text-left py-2 text-gray-300">Round</th>
                        <th className="text-left py-2 text-gray-300">Max Allocation</th>
                        <th className="text-left py-2 text-gray-300">Used</th>
                        <th className="text-left py-2 text-gray-300">Claimable</th>
                        <th className="text-left py-2 text-gray-300">Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      {oldMemberships.map((membership) => {
                        const claimableAmount = BigInt(membership.usage.max) - BigInt(membership.usage.current);
                        const canMigrate = claimableAmount > 0n;
                        
                        return (
                          <tr key={membership.id} className="border-b border-gray-800">
                            <td className="py-2 text-gray-300">{membership.id}</td>
                            <td className="py-2 text-gray-300">{membership.roundId}</td>
                            <td className="py-2 text-gray-300">
                              {formatTokenAmount(BigInt(membership.usage.max), vestedToken.decimals, vestedToken.symbol)}
                            </td>
                            <td className="py-2 text-gray-300">
                              {formatTokenAmount(BigInt(membership.usage.current), vestedToken.decimals, vestedToken.symbol)}
                            </td>
                            <td className="py-2 text-gray-300">
                              {formatTokenAmount(claimableAmount, vestedToken.decimals, vestedToken.symbol)}
                            </td>
                            <td className="py-2">
                              <span className={`px-2 py-1 rounded text-xs ${
                                canMigrate 
                                  ? 'bg-green-900/50 text-green-400 border border-green-700' 
                                  : 'bg-gray-900/50 text-gray-400 border border-gray-700'
                              }`}>
                                {canMigrate ? 'Can Migrate' : 'Fully Used'}
                              </span>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* New Memberships Details */}
      <Card className="bg-gray-900/50 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="text-white">New Contract Memberships</span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => toggleSection('new')}
            >
              {expandedSection === 'new' ? 'Collapse' : 'Expand'}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {expandedSection === 'new' && (
            <div className="space-y-4">
              {newMemberships.length === 0 ? (
                <p className="text-gray-400">No new memberships found.</p>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-gray-700">
                        <th className="text-left py-2 text-gray-300">ID</th>
                        <th className="text-left py-2 text-gray-300">Round</th>
                        <th className="text-left py-2 text-gray-300">Max Allocation</th>
                        <th className="text-left py-2 text-gray-300">Used</th>
                        <th className="text-left py-2 text-gray-300">Remaining</th>
                        <th className="text-left py-2 text-gray-300">Locked</th>
                        <th className="text-left py-2 text-gray-300">Has Proofs</th>
                      </tr>
                    </thead>
                    <tbody>
                      {newMemberships.map((membership) => {
                        const remainingAmount = BigInt(membership.usage.max) - BigInt(membership.usage.current);
                        
                        return (
                          <tr key={membership.id} className="border-b border-gray-800">
                            <td className="py-2 text-gray-300">{membership.id}</td>
                            <td className="py-2 text-gray-300">{membership.roundId}</td>
                            <td className="py-2 text-gray-300">
                              {formatTokenAmount(BigInt(membership.usage.max), vestedToken.decimals, vestedToken.symbol)}
                            </td>
                            <td className="py-2 text-gray-300">
                              {formatTokenAmount(BigInt(membership.usage.current), vestedToken.decimals, vestedToken.symbol)}
                            </td>
                            <td className="py-2 text-gray-300">
                              {formatTokenAmount(remainingAmount, vestedToken.decimals, vestedToken.symbol)}
                            </td>
                            <td className="py-2 text-gray-300">
                              {formatTokenAmount(BigInt(membership.locked), vestedToken.decimals, vestedToken.symbol)}
                            </td>
                            <td className="py-2">
                              <span className={`px-2 py-1 rounded text-xs ${
                                membership.proofs && membership.proofs.length > 0
                                  ? 'bg-blue-900/50 text-blue-400 border border-blue-700' 
                                  : 'bg-gray-900/50 text-gray-400 border border-gray-700'
                              }`}>
                                {membership.proofs && membership.proofs.length > 0 ? 'Yes' : 'No'}
                              </span>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* User Lookup Detailed Results */}
      {userLookupData && !userLookupData.error && !userLookupData.loading && (
        <>
          {/* User's Old Memberships */}
          {userLookupData.oldMemberships.length > 0 && (
            <Card className="bg-gray-900/50 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">
                  User's Old Contract Memberships ({userLookupData.oldMemberships.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-gray-700">
                        <th className="text-left py-2 text-gray-300">ID</th>
                        <th className="text-left py-2 text-gray-300">Round</th>
                        <th className="text-left py-2 text-gray-300">Max Allocation</th>
                        <th className="text-left py-2 text-gray-300">Used</th>
                        <th className="text-left py-2 text-gray-300">Claimable</th>
                        <th className="text-left py-2 text-gray-300">Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      {userLookupData.oldMemberships.map((membership) => {
                        const claimableAmount = BigInt(membership.usage.max) - BigInt(membership.usage.current);
                        const canMigrate = claimableAmount > 0n;
                        
                        return (
                          <tr key={membership.id} className="border-b border-gray-800">
                            <td className="py-2 text-gray-300">{membership.id}</td>
                            <td className="py-2 text-gray-300">{membership.roundId}</td>
                            <td className="py-2 text-gray-300">
                              {formatTokenAmount(BigInt(membership.usage.max), vestedToken.decimals, vestedToken.symbol)}
                            </td>
                            <td className="py-2 text-gray-300">
                              {formatTokenAmount(BigInt(membership.usage.current), vestedToken.decimals, vestedToken.symbol)}
                            </td>
                            <td className="py-2 text-gray-300">
                              {formatTokenAmount(claimableAmount, vestedToken.decimals, vestedToken.symbol)}
                            </td>
                            <td className="py-2">
                              <span className={`px-2 py-1 rounded text-xs ${
                                canMigrate 
                                  ? 'bg-green-900/50 text-green-400 border border-green-700' 
                                  : 'bg-gray-900/50 text-gray-400 border border-gray-700'
                              }`}>
                                {canMigrate ? 'Can Migrate' : 'Fully Used'}
                              </span>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          )}

          {/* User's New Memberships */}
          {userLookupData.newMemberships.length > 0 && (
            <Card className="bg-gray-900/50 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">
                  User's New Contract Memberships ({userLookupData.newMemberships.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-gray-700">
                        <th className="text-left py-2 text-gray-300">ID</th>
                        <th className="text-left py-2 text-gray-300">Round</th>
                        <th className="text-left py-2 text-gray-300">Max Allocation</th>
                        <th className="text-left py-2 text-gray-300">Used</th>
                        <th className="text-left py-2 text-gray-300">Remaining</th>
                        <th className="text-left py-2 text-gray-300">Locked</th>
                        <th className="text-left py-2 text-gray-300">Has Proofs</th>
                      </tr>
                    </thead>
                    <tbody>
                      {userLookupData.newMemberships.map((membership) => {
                        const remainingAmount = BigInt(membership.usage.max) - BigInt(membership.usage.current);
                        
                        return (
                          <tr key={membership.id} className="border-b border-gray-800">
                            <td className="py-2 text-gray-300">{membership.id}</td>
                            <td className="py-2 text-gray-300">{membership.roundId}</td>
                            <td className="py-2 text-gray-300">
                              {formatTokenAmount(BigInt(membership.usage.max), vestedToken.decimals, vestedToken.symbol)}
                            </td>
                            <td className="py-2 text-gray-300">
                              {formatTokenAmount(BigInt(membership.usage.current), vestedToken.decimals, vestedToken.symbol)}
                            </td>
                            <td className="py-2 text-gray-300">
                              {formatTokenAmount(remainingAmount, vestedToken.decimals, vestedToken.symbol)}
                            </td>
                            <td className="py-2 text-gray-300">
                              {formatTokenAmount(BigInt(membership.locked), vestedToken.decimals, vestedToken.symbol)}
                            </td>
                            <td className="py-2">
                              <span className={`px-2 py-1 rounded text-xs ${
                                membership.proofs && membership.proofs.length > 0
                                  ? 'bg-blue-900/50 text-blue-400 border border-blue-700' 
                                  : 'bg-gray-900/50 text-gray-400 border border-gray-700'
                              }`}>
                                {membership.proofs && membership.proofs.length > 0 ? 'Yes' : 'No'}
                              </span>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          )}

          {/* No memberships found message */}
          {userLookupData.oldMemberships.length === 0 && userLookupData.newMemberships.length === 0 && (
            <Card className="bg-yellow-900/20 border-yellow-700">
              <CardContent className="py-6">
                <div className="text-center text-yellow-400">
                  <h4 className="font-medium mb-2">No Memberships Found</h4>
                  <p className="text-sm text-gray-300">
                    This address doesn't have any memberships in either the old or new contracts.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}

      {/* Rounds Information */}
      <Card className="bg-gray-900/50 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="text-white">Presale Rounds</span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => toggleSection('rounds')}
            >
              {expandedSection === 'rounds' ? 'Collapse' : 'Expand'}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {expandedSection === 'rounds' && (
            <div className="space-y-4">
              {!presaleData?.rounds || presaleData.rounds.length === 0 ? (
                <p className="text-gray-400">No rounds found.</p>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {presaleData.rounds.map((round: any) => (
                    <Card key={round.roundId} className="bg-gray-800/50 border-gray-600">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm text-white">
                          Round {round.roundId} {round.roundId === selectedRoundId && '(Active)'}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="text-xs text-gray-400 space-y-1">
                        <div>Start: {new Date(Number(round.startTimestamp) * 1000).toLocaleString()}</div>
                        <div>End: {new Date(Number(round.endTimestamp) * 1000).toLocaleString()}</div>
                        <div>State: {round.state === 0 ? 'Pending' : round.state === 1 ? 'Active' : 'Vesting'}</div>
                        <div>Name: {round.name || 'Unnamed'}</div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}