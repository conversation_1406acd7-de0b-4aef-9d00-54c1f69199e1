import { useContext, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAccount } from 'wagmi';
import { formatUnits, isAddress, Address } from 'viem';

import { Membership } from '@/class/interface/presale';
import { MembershipsContext } from '@/providers/membership-provider';
import { OldMembershipsContext } from '@/providers/old-membership-provider';
import { PresaleContext } from '@/providers/presale-provider';
import { ProjectConfigContext } from '@/providers/project-config-provider';
import { PublicClientContext } from '@/providers/public-client-provider';
import { TokensContext } from '@/providers/tokens-provider';
import { cutDecimals } from '@/lib/cut-decimals';

import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Spinner } from './icons/spinner';
import { Input } from './ui/input';
import { Label } from './ui/label';

interface WhitelistEntry {
  address: Address;
  price: number;
  allocation: string;
  claimbackPeriod: number;
  tgeNumerator: number;
  tgeDenominator: number;
  cliffDuration: number;
  cliffNumerator: number;
  cliffDenominator: number;
  vestingPeriodCount: number;
  vestingPeriodDuration: number;
  tradeable: number;
  proofs: string[];
  round?: string;
  tier?: number;
}

interface WhitelistStats {
  totalWhitelistedUsers: number;
  totalAllocation: bigint;
  roundBreakdown: { [round: string]: { [tier: string]: number } };
  averageAllocation: bigint;
}

interface AdminStats {
  totalOldMemberships: number;
  totalNewMemberships: number;
  totalOldFunds: bigint;
  totalNewFunds: bigint;
  totalMigrationCandidates: number;
  connectedUsers: number;
  whitelistStats: WhitelistStats;
}

interface UserStats {
  totalOldMemberships: number;
  totalNewMemberships: number;
  totalOldFunds: bigint;
  totalNewFunds: bigint;
  totalMigrationCandidates: number;
  address: Address;
  whitelistEntries: WhitelistEntry[];
}

interface UserLookupData {
  address: Address;
  oldMemberships: Membership[];
  newMemberships: Membership[];
  whitelistEntries: WhitelistEntry[];
  loading: boolean;
  error: string | null;
}

export default function AdminPage() {
  const { address, isConnected } = useAccount();
  const { memberships: newMemberships, loading: newLoading } = useContext(MembershipsContext);
  const { memberships: oldMemberships, loading: oldLoading } = useContext(OldMembershipsContext);
  const { presaleInstance, presaleData, selectedRoundId } = useContext(PresaleContext);
  const { chain, vestedToken, collectedToken } = useContext(ProjectConfigContext);
  const { vestedToken: _vestedTokenData, collectedToken: _collectedTokenData } = useContext(TokensContext);
  const { publicClient } = useContext(PublicClientContext);

  const [adminStats, setAdminStats] = useState<AdminStats>({
    totalOldMemberships: 0,
    totalNewMemberships: 0,
    totalOldFunds: 0n,
    totalNewFunds: 0n,
    totalMigrationCandidates: 0,
    connectedUsers: 0,
    whitelistStats: {
      totalWhitelistedUsers: 0,
      totalAllocation: 0n,
      roundBreakdown: {},
      averageAllocation: 0n,
    },
  });

  const [expandedSection, setExpandedSection] = useState<string | null>(null);
  const [userLookupAddress, setUserLookupAddress] = useState<string>('');
  const [userLookupData, setUserLookupData] = useState<UserLookupData | null>(null);
  const [isLookingUp, setIsLookingUp] = useState(false);
  const navigate = useNavigate();
  // Check if user is admin (you can implement your own admin check logic)


  useEffect(() => {
    const isAdmin = address && (
      address.toLowerCase() === '0x979f05722488a1d134c109dcf77a9af584998825'
    );
    if (!isAdmin) {
      navigate('/');
    }
  }, [address])

  useEffect(() => {
    if (!newLoading && !oldLoading) {
      calculateStats();
    }
  }, [newMemberships, oldMemberships, newLoading, oldLoading]);

  const calculateStats = () => {
    // Calculate total funds in old memberships
    const totalOldFunds = oldMemberships.reduce((sum, membership) => {
      const claimableAmount = BigInt(membership.usage.max) - BigInt(membership.usage.current);
      return sum + claimableAmount;
    }, 0n);

    // Calculate total funds in new memberships
    const totalNewFunds = newMemberships.reduce((sum, membership) => {
      return sum + BigInt(membership.usage.current);
    }, 0n);

    // Count migration candidates (old memberships with claimable funds)
    const migrationCandidates = oldMemberships.filter(membership => {
      const claimableAmount = BigInt(membership.usage.max) - BigInt(membership.usage.current);
      return claimableAmount > 0n;
    }).length;

    // Fetch whitelist stats
    fetchWhitelistStats().then(whitelistStats => {
      setAdminStats({
        totalOldMemberships: oldMemberships.length,
        totalNewMemberships: newMemberships.length,
        totalOldFunds,
        totalNewFunds,
        totalMigrationCandidates: migrationCandidates,
        connectedUsers: isConnected ? 1 : 0, // This would need to be tracked differently in a real app
        whitelistStats,
      });
    });
  };

  // Fetch whitelist data for a specific user
  const fetchUserWhitelistData = async (address: Address): Promise<WhitelistEntry[]> => {
    const whitelistEntries: WhitelistEntry[] = [];
    const rounds = ['tglp', 'fcfs'];
    const versions = ['new', 'old'];
    const tiers = [1, 2, 3];

    for (const round of rounds) {
      for (const version of versions) {
        for (const tier of tiers) {
          try {
            const response = await fetch(`/proofs/${round}/${version}/${tier}/${address.toLowerCase()}.json`);
            if (response.ok) {
              const data = await response.json();
              whitelistEntries.push({
                ...data,
                round,
                tier,
              });
            }
          } catch (error) {
            // File doesn't exist, continue
          }
        }
      }
    }

    return whitelistEntries;
  };

  // Fetch global whitelist statistics
  const fetchWhitelistStats = async (): Promise<WhitelistStats> => {
    // This is a simplified version - in a real implementation, you might want to
    // fetch a pre-computed summary or implement server-side aggregation
    const stats: WhitelistStats = {
      totalWhitelistedUsers: 0,
      totalAllocation: 0n,
      roundBreakdown: {},
      averageAllocation: 0n,
    };

    // For now, return basic stats - you could enhance this to fetch actual data
    // from a summary endpoint or aggregate from individual files
    return stats;
  };

  const calculateUserStats = (userData: UserLookupData): UserStats => {
    // Calculate total funds in user's old memberships
    const totalOldFunds = userData.oldMemberships.reduce((sum, membership) => {
      const claimableAmount = BigInt(membership.usage.max) - BigInt(membership.usage.current);
      return sum + claimableAmount;
    }, 0n);
    console.log("🚀 ~ totalOldFunds ~ totalOldFunds:", totalOldFunds)

    // Calculate total funds in user's new memberships
    const totalNewFunds = userData.newMemberships.reduce((sum, membership) => {
      return sum + BigInt(membership.usage.current);
    }, 0n);

    // Count user's migration candidates (old memberships with claimable funds)
    const migrationCandidates = userData.oldMemberships.filter(membership => {
      const claimableAmount = BigInt(membership.usage.max) - BigInt(membership.usage.current);
      return claimableAmount > 0n;
    }).length;

    return {
      totalOldMemberships: userData.oldMemberships.length,
      totalNewMemberships: userData.newMemberships.length,
      totalOldFunds,
      totalNewFunds,
      totalMigrationCandidates: migrationCandidates,
      address: userData.address,
      whitelistEntries: userData.whitelistEntries,
    };
  };

  const toggleSection = (section: string) => {
    setExpandedSection(expandedSection === section ? null : section);
  };

  const formatTokenAmount = (amount: bigint, decimals: number, symbol: string) => {
    return `${cutDecimals(formatUnits(amount, decimals), 4)} ${symbol}`;
  };

  // Handle user lookup
  const handleUserLookup = async () => {
    if (!userLookupAddress.trim()) {
      return;
    }

    if (!isAddress(userLookupAddress)) {
      setUserLookupData({
        address: userLookupAddress as Address,
        oldMemberships: [],
        newMemberships: [],
        whitelistEntries: [],
        loading: false,
        error: 'Invalid Ethereum address format'
      });
      return;
    }

    setIsLookingUp(true);
    setUserLookupData({
      address: userLookupAddress as Address,
      oldMemberships: [],
      newMemberships: [],
      whitelistEntries: [],
      loading: true,
      error: null
    });

    try {
      // Fetch old memberships
      let userOldMemberships: Membership[] = [];
      if (presaleInstance) {
        try {
          // Create old presale instance to fetch old memberships
          const { PresaleFactory } = await import('@/class/presale-factory');
          const oldPresaleInstance = await PresaleFactory.createInstance(
            'v3', // or 'v4' based on old contract version
            publicClient,
            '******************************************' as Address
          );
          userOldMemberships = await oldPresaleInstance.getMemberships(userLookupAddress as Address);
        } catch (error) {
          console.error('Error fetching old memberships for user:', error);
        }
      }

      // Fetch new memberships
      let userNewMemberships: Membership[] = [];
      if (presaleInstance) {
        try {
          userNewMemberships = await presaleInstance.getMemberships(userLookupAddress as Address);
        } catch (error) {
          console.error('Error fetching new memberships for user:', error);
        }
      }

      // Fetch whitelist data for the user
      const userWhitelistEntries = await fetchUserWhitelistData(userLookupAddress as Address);

      setUserLookupData({
        address: userLookupAddress as Address,
        oldMemberships: userOldMemberships,
        newMemberships: userNewMemberships,
        whitelistEntries: userWhitelistEntries,
        loading: false,
        error: null
      });
    } catch (error) {
      console.error('User lookup failed:', error);
      setUserLookupData({
        address: userLookupAddress as Address,
        oldMemberships: [],
        newMemberships: [],
        whitelistEntries: [],
        loading: false,
        error: 'Failed to fetch user data. Please try again.'
      });
    } finally {
      setIsLookingUp(false);
    }
  };

  const clearUserLookup = () => {
    setUserLookupAddress('');
    setUserLookupData(null);
  };


  if (newLoading || oldLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center py-12">
          <Spinner className="animate-spin w-8 h-8 mr-3" />
          <span className="text-gray-300">Loading admin data...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-white">Admin Dashboard</h1>
        <div className="text-sm text-gray-400">
          Connected as: {address ? `${address.slice(0, 6)}...${address.slice(-4)}` : 'Not connected'}
        </div>
      </div>

      {/* User Lookup Section */}
      <Card className="bg-purple-900/20 border-purple-700">
        <CardHeader>
          <CardTitle className="text-purple-400">User Lookup</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <div className="flex-1">
              <Label htmlFor="userAddress" className="text-sm text-gray-300">
                Enter User Address
              </Label>
              <Input
                id="userAddress"
                type="text"
                placeholder="0x..."
                value={userLookupAddress}
                onChange={(e) => setUserLookupAddress(e.target.value)}
                className="mt-1"
                disabled={isLookingUp}
              />
            </div>
            <div className="flex items-end gap-2">
              <Button
                onClick={handleUserLookup}
                disabled={isLookingUp || !userLookupAddress.trim()}
                className="bg-purple-600 hover:bg-purple-700"
              >
                {isLookingUp ? (
                  <>
                    <Spinner className="animate-spin w-4 h-4 mr-2" />
                    Looking up...
                  </>
                ) : (
                  'Lookup'
                )}
              </Button>
              {userLookupData && (
                <Button
                  variant="outline"
                  onClick={clearUserLookup}
                  disabled={isLookingUp}
                >
                  Clear
                </Button>
              )}
            </div>
          </div>

          {userLookupData && (
            <div className="mt-4 p-4 bg-gray-800/50 rounded-lg border border-gray-700">
              <h4 className="text-sm font-medium text-gray-300 mb-3">
                Results for: {userLookupData.address.slice(0, 6)}...{userLookupData.address.slice(-4)}
              </h4>

              {userLookupData.error ? (
                <div className="text-red-400 text-sm">{userLookupData.error}</div>
              ) : userLookupData.loading ? (
                <div className="flex items-center gap-2 text-gray-400">
                  <Spinner className="animate-spin w-4 h-4" />
                  <span>Loading user data...</span>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h5 className="text-xs font-medium text-blue-400 mb-2">Old Contract</h5>
                    <div className="text-sm text-gray-300">
                      <div>Memberships: {userLookupData.oldMemberships.length}</div>
                      <div>
                        Total Claimable: {formatTokenAmount(
                          userLookupData.oldMemberships.reduce((sum, m) =>
                            sum + (BigInt(m.usage.max) - BigInt(m.usage.current)), 0n
                          ),
                          vestedToken.decimals,
                          vestedToken.symbol
                        )}
                      </div>
                    </div>
                  </div>
                  <div>
                    <h5 className="text-xs font-medium text-green-400 mb-2">New Contract</h5>
                    <div className="text-sm text-gray-300">
                      <div>Memberships: {userLookupData.newMemberships.length}</div>
                      <div>
                        Total Invested: {formatTokenAmount(
                          userLookupData.newMemberships.reduce((sum, m) =>
                            sum + BigInt(m.usage.current), 0n
                          ),
                          vestedToken.decimals,
                          vestedToken.symbol
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Overview Stats */}
      <div className="space-y-4">
        {/* Stats Header */}
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-white">
            {userLookupData && !userLookupData.error && !userLookupData.loading
              ? `Statistics for ${userLookupData.address.slice(0, 6)}...${userLookupData.address.slice(-4)}`
              : 'Global Statistics'
            }
          </h2>
          {userLookupData && !userLookupData.error && !userLookupData.loading && (
            <span className="text-xs text-purple-400 bg-purple-900/20 px-2 py-1 rounded border border-purple-700">
              User View
            </span>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {(() => {
            // Calculate which stats to display
            const displayStats = userLookupData && !userLookupData.error && !userLookupData.loading
              ? calculateUserStats(userLookupData)
              : adminStats;

            return (
              <>
                <Card className="bg-blue-900/20 border-blue-700">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-blue-400 text-sm">Old Contract Memberships</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-white">{displayStats.totalOldMemberships}</div>
                    <div className="text-xs text-gray-400">
                      Total funds: {formatTokenAmount(displayStats.totalOldFunds, vestedToken.decimals, vestedToken.symbol)}
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-green-900/20 border-green-700">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-green-400 text-sm">New Contract Memberships</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-white">{displayStats.totalNewMemberships}</div>
                    <div className="text-xs text-gray-400">
                      Total invested: {formatTokenAmount(displayStats.totalNewFunds, vestedToken.decimals, vestedToken.symbol)}
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-yellow-900/20 border-yellow-700">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-yellow-400 text-sm">Migration Candidates</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-white">{displayStats.totalMigrationCandidates}</div>
                    <div className="text-xs text-gray-400">
                      Memberships with claimable funds
                    </div>
                  </CardContent>
                </Card>

                {/* Whitelist Information - Only show for global stats or user-specific */}
                {(() => {
                  if (userLookupData && !userLookupData.error && !userLookupData.loading) {
                    // User-specific whitelist info
                    return (
                      <Card className="bg-purple-900/20 border-purple-700">
                        <CardHeader className="pb-2">
                          <CardTitle className="text-purple-400 text-sm">Whitelist Status</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold text-white">{userLookupData.whitelistEntries.length}</div>
                          <div className="text-xs text-gray-400">
                            Whitelist entries found
                          </div>
                        </CardContent>
                      </Card>
                    );
                  } else {
                    // Global whitelist stats
                    return (
                      <Card className="bg-purple-900/20 border-purple-700">
                        <CardHeader className="pb-2">
                          <CardTitle className="text-purple-400 text-sm">Whitelisted Users</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold text-white">{adminStats.whitelistStats.totalWhitelistedUsers}</div>
                          <div className="text-xs text-gray-400">
                            Total allocation: {formatTokenAmount(adminStats.whitelistStats.totalAllocation, vestedToken.decimals, vestedToken.symbol)}
                          </div>
                        </CardContent>
                      </Card>
                    );
                  }
                })()}
              </>
            );
          })()}
        </div>
      </div>

      {/* Contract Information - Hidden during user lookup */}
      {!(userLookupData && !userLookupData.error && !userLookupData.loading) && (
        <Card className="bg-gray-900/50 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white">Contract Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm font-medium text-gray-300 mb-2">Current Presale Contract</h4>
                <div className="text-xs text-gray-400 space-y-1">
                  <div>Address: {presaleInstance?.getPresaleData().presaleContractAddress || 'Not loaded'}</div>
                  <div>Version: {presaleInstance?.getVersion() || 'Unknown'}</div>
                  <div>Active Round: {selectedRoundId}</div>
                </div>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-300 mb-2">Token Information</h4>
                <div className="text-xs text-gray-400 space-y-1">
                  <div>Vested Token: {vestedToken.symbol} ({vestedToken.address})</div>
                  <div>Collected Token: {collectedToken.symbol} ({collectedToken.address})</div>
                  <div>Chain: {chain.name} (ID: {chain.id})</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Old Memberships Details - Hidden during user lookup */}
      {!(userLookupData && !userLookupData.error && !userLookupData.loading) && (
        <Card className="bg-gray-900/50 border-gray-700">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="text-white">Old Contract Memberships</span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => toggleSection('old')}
              >
                {expandedSection === 'old' ? 'Collapse' : 'Expand'}
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {expandedSection === 'old' && (
              <div className="space-y-4">
                {oldMemberships.length === 0 ? (
                  <p className="text-gray-400">No old memberships found.</p>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b border-gray-700">
                          <th className="text-left py-2 text-gray-300">ID</th>
                          <th className="text-left py-2 text-gray-300">Round</th>
                          <th className="text-left py-2 text-gray-300">Max Allocation</th>
                          <th className="text-left py-2 text-gray-300">Used</th>
                          <th className="text-left py-2 text-gray-300">Claimable</th>
                          <th className="text-left py-2 text-gray-300">Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        {oldMemberships.map((membership) => {
                          const claimableAmount = BigInt(membership.usage.max) - BigInt(membership.usage.current);
                          const canMigrate = claimableAmount > 0n;

                          return (
                            <tr key={membership.id} className="border-b border-gray-800">
                              <td className="py-2 text-gray-300">{membership.id}</td>
                              <td className="py-2 text-gray-300">{membership.roundId}</td>
                              <td className="py-2 text-gray-300">
                                {formatTokenAmount(BigInt(membership.usage.max), vestedToken.decimals, vestedToken.symbol)}
                              </td>
                              <td className="py-2 text-gray-300">
                                {formatTokenAmount(BigInt(membership.usage.current), vestedToken.decimals, vestedToken.symbol)}
                              </td>
                              <td className="py-2 text-gray-300">
                                {formatTokenAmount(claimableAmount, vestedToken.decimals, vestedToken.symbol)}
                              </td>
                              <td className="py-2">
                                <span className={`px-2 py-1 rounded text-xs ${canMigrate
                                    ? 'bg-green-900/50 text-green-400 border border-green-700'
                                    : 'bg-gray-900/50 text-gray-400 border border-gray-700'
                                  }`}>
                                  {canMigrate ? 'Can Migrate' : 'Fully Used'}
                                </span>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* New Memberships Details - Hidden during user lookup */}
      {!(userLookupData && !userLookupData.error && !userLookupData.loading) && (
        <Card className="bg-gray-900/50 border-gray-700">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="text-white">New Contract Memberships</span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => toggleSection('new')}
              >
                {expandedSection === 'new' ? 'Collapse' : 'Expand'}
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {expandedSection === 'new' && (
              <div className="space-y-4">
                {newMemberships.length === 0 ? (
                  <p className="text-gray-400">No new memberships found.</p>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b border-gray-700">
                          <th className="text-left py-2 text-gray-300">ID</th>
                          <th className="text-left py-2 text-gray-300">Round</th>
                          <th className="text-left py-2 text-gray-300">Max Allocation</th>
                          <th className="text-left py-2 text-gray-300">Used</th>
                          <th className="text-left py-2 text-gray-300">Remaining</th>
                          <th className="text-left py-2 text-gray-300">Locked</th>
                          <th className="text-left py-2 text-gray-300">Has Proofs</th>
                        </tr>
                      </thead>
                      <tbody>
                        {newMemberships.map((membership) => {
                          const remainingAmount = BigInt(membership.usage.max) - BigInt(membership.usage.current);

                          return (
                            <tr key={membership.id} className="border-b border-gray-800">
                              <td className="py-2 text-gray-300">{membership.id}</td>
                              <td className="py-2 text-gray-300">{membership.roundId}</td>
                              <td className="py-2 text-gray-300">
                                {formatTokenAmount(BigInt(membership.usage.max), vestedToken.decimals, vestedToken.symbol)}
                              </td>
                              <td className="py-2 text-gray-300">
                                {formatTokenAmount(BigInt(membership.usage.current), vestedToken.decimals, vestedToken.symbol)}
                              </td>
                              <td className="py-2 text-gray-300">
                                {formatTokenAmount(remainingAmount, vestedToken.decimals, vestedToken.symbol)}
                              </td>
                              <td className="py-2 text-gray-300">
                                {formatTokenAmount(BigInt(membership.locked), vestedToken.decimals, vestedToken.symbol)}
                              </td>
                              <td className="py-2">
                                <span className={`px-2 py-1 rounded text-xs ${membership.proofs && membership.proofs.length > 0
                                    ? 'bg-blue-900/50 text-blue-400 border border-blue-700'
                                    : 'bg-gray-900/50 text-gray-400 border border-gray-700'
                                  }`}>
                                  {membership.proofs && membership.proofs.length > 0 ? 'Yes' : 'No'}
                                </span>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* User Lookup Detailed Results */}
      {userLookupData && !userLookupData.error && !userLookupData.loading && (
        <>
          {/* User's Old Memberships */}
          {userLookupData.oldMemberships.length > 0 && (
            <Card className="bg-gray-900/50 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">
                  User's Old Contract Memberships ({userLookupData.oldMemberships.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-gray-700">
                        <th className="text-left py-2 text-gray-300">ID</th>
                        <th className="text-left py-2 text-gray-300">Round</th>
                        <th className="text-left py-2 text-gray-300">Max Allocation</th>
                        <th className="text-left py-2 text-gray-300">Used</th>
                        <th className="text-left py-2 text-gray-300">Claimable</th>
                        <th className="text-left py-2 text-gray-300">Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      {userLookupData.oldMemberships.map((membership) => {
                        const claimableAmount = BigInt(membership.usage.max) - BigInt(membership.usage.current);
                        const canMigrate = claimableAmount > 0n;

                        return (
                          <tr key={membership.id} className="border-b border-gray-800">
                            <td className="py-2 text-gray-300">{membership.id}</td>
                            <td className="py-2 text-gray-300">{membership.roundId}</td>
                            <td className="py-2 text-gray-300">
                              {formatTokenAmount(BigInt(membership.usage.max), vestedToken.decimals, vestedToken.symbol)}
                            </td>
                            <td className="py-2 text-gray-300">
                              {formatTokenAmount(BigInt(membership.usage.current), vestedToken.decimals, vestedToken.symbol)}
                            </td>
                            <td className="py-2 text-gray-300">
                              {formatTokenAmount(claimableAmount, vestedToken.decimals, vestedToken.symbol)}
                            </td>
                            <td className="py-2">
                              <span className={`px-2 py-1 rounded text-xs ${canMigrate
                                  ? 'bg-green-900/50 text-green-400 border border-green-700'
                                  : 'bg-gray-900/50 text-gray-400 border border-gray-700'
                                }`}>
                                {canMigrate ? 'Can Migrate' : 'Fully Used'}
                              </span>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          )}

          {/* User's New Memberships */}
          {userLookupData.newMemberships.length > 0 && (
            <Card className="bg-gray-900/50 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">
                  User's New Contract Memberships ({userLookupData.newMemberships.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-gray-700">
                        <th className="text-left py-2 text-gray-300">ID</th>
                        <th className="text-left py-2 text-gray-300">Round</th>
                        <th className="text-left py-2 text-gray-300">Max Allocation</th>
                        <th className="text-left py-2 text-gray-300">Used</th>
                        <th className="text-left py-2 text-gray-300">Remaining</th>
                        <th className="text-left py-2 text-gray-300">Locked</th>
                        <th className="text-left py-2 text-gray-300">Has Proofs</th>
                      </tr>
                    </thead>
                    <tbody>
                      {userLookupData.newMemberships.map((membership) => {
                        const remainingAmount = BigInt(membership.usage.max) - BigInt(membership.usage.current);

                        return (
                          <tr key={membership.id} className="border-b border-gray-800">
                            <td className="py-2 text-gray-300">{membership.id}</td>
                            <td className="py-2 text-gray-300">{membership.roundId}</td>
                            <td className="py-2 text-gray-300">
                              {formatTokenAmount(BigInt(membership.usage.max), vestedToken.decimals, vestedToken.symbol)}
                            </td>
                            <td className="py-2 text-gray-300">
                              {formatTokenAmount(BigInt(membership.usage.current), vestedToken.decimals, vestedToken.symbol)}
                            </td>
                            <td className="py-2 text-gray-300">
                              {formatTokenAmount(remainingAmount, vestedToken.decimals, vestedToken.symbol)}
                            </td>
                            <td className="py-2 text-gray-300">
                              {formatTokenAmount(BigInt(membership.locked), vestedToken.decimals, vestedToken.symbol)}
                            </td>
                            <td className="py-2">
                              <span className={`px-2 py-1 rounded text-xs ${membership.proofs && membership.proofs.length > 0
                                  ? 'bg-blue-900/50 text-blue-400 border border-blue-700'
                                  : 'bg-gray-900/50 text-gray-400 border border-gray-700'
                                }`}>
                                {membership.proofs && membership.proofs.length > 0 ? 'Yes' : 'No'}
                              </span>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          )}

          {/* No memberships found message */}
          {userLookupData.oldMemberships.length === 0 && userLookupData.newMemberships.length === 0 && (
            <Card className="bg-yellow-900/20 border-yellow-700">
              <CardContent className="py-6">
                <div className="text-center text-yellow-400">
                  <h4 className="font-medium mb-2">No Memberships Found</h4>
                  <p className="text-sm text-gray-300">
                    This address doesn't have any memberships in either the old or new contracts.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
          {/* User's Whitelist Information */}
          {userLookupData.whitelistEntries.length > 0 && (
            <Card className="bg-gray-900/50 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">
                  Whitelist Information ({userLookupData.whitelistEntries.length} entries)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-gray-700">
                        <th className="text-left py-2 text-gray-300">Round</th>
                        <th className="text-left py-2 text-gray-300">Tier</th>
                        <th className="text-left py-2 text-gray-300">Allocation</th>
                        <th className="text-left py-2 text-gray-300">Price</th>
                        <th className="text-left py-2 text-gray-300">TGE %</th>
                        <th className="text-left py-2 text-gray-300">Vesting</th>
                        <th className="text-left py-2 text-gray-300">Tradeable</th>
                      </tr>
                    </thead>
                    <tbody>
                      {userLookupData.whitelistEntries.map((entry, index) => (
                        <tr key={index} className="border-b border-gray-800">
                          <td className="py-2 text-white">{entry.round?.toUpperCase()}</td>
                          <td className="py-2 text-white">{entry.tier}</td>
                          <td className="py-2 text-white">
                            {formatTokenAmount(BigInt(entry.allocation), vestedToken.decimals, vestedToken.symbol)}
                          </td>
                          <td className="py-2 text-white">${(entry.price / 1000000).toFixed(6)}</td>
                          <td className="py-2 text-white">{entry.tgeNumerator}/{entry.tgeDenominator} ({((entry.tgeNumerator / entry.tgeDenominator) * 100).toFixed(1)}%)</td>
                          <td className="py-2 text-white">{entry.vestingPeriodCount} periods</td>
                          <td className="py-2">
                            <span className={`px-2 py-1 rounded text-xs ${entry.tradeable ? 'bg-green-900/50 text-green-400' : 'bg-red-900/50 text-red-400'}`}>
                              {entry.tradeable ? 'Yes' : 'No'}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          )}

          {/* No Whitelist Entries */}
          {userLookupData.whitelistEntries.length === 0 && (
            <Card className="bg-gray-900/50 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white">Whitelist Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <p className="text-gray-400">
                    This address is not found in any whitelist rounds.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}

      {/* Rounds Information - Hidden during user lookup */}
      {!(userLookupData && !userLookupData.error && !userLookupData.loading) && (
        <Card className="bg-gray-900/50 border-gray-700">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="text-white">Presale Rounds</span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => toggleSection('rounds')}
              >
                {expandedSection === 'rounds' ? 'Collapse' : 'Expand'}
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {expandedSection === 'rounds' && (
              <div className="space-y-4">
                {!presaleData?.rounds || presaleData.rounds.length === 0 ? (
                  <p className="text-gray-400">No rounds found.</p>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {presaleData.rounds.map((round: any) => (
                      <Card key={round.roundId} className="bg-gray-800/50 border-gray-600">
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm text-white">
                            Round {round.roundId} {round.roundId === selectedRoundId && '(Active)'}
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="text-xs text-gray-400 space-y-1">
                          <div>Start: {new Date(Number(round.startTimestamp) * 1000).toLocaleString()}</div>
                          <div>End: {new Date(Number(round.endTimestamp) * 1000).toLocaleString()}</div>
                          <div>State: {round.state === 0 ? 'Pending' : round.state === 1 ? 'Active' : 'Vesting'}</div>
                          <div>Name: {round.name || 'Unnamed'}</div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}