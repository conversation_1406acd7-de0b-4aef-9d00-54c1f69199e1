import { useContext } from 'react';
import { Link, Outlet, useLocation } from 'react-router-dom';
import { useAccount, useConnect, useSwitchChain } from 'wagmi';

import { lsSet } from '@/lib/local-storage';
import { MembershipsProvider } from '@/providers/membership-provider';
import { OldMembershipsProvider } from '@/providers/old-membership-provider';
import { ProjectConfigContext } from '@/providers/project-config-provider';
import { ThemeContext } from '@/providers/theme-provider';
import { TokensProvider } from '@/providers/tokens-provider';

import { Spinner } from './icons/spinner';
import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import AdminNavigation from './admin-navigation';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from './ui/dialog';

export default function AppLayout() {
  const { company, chain } = useContext(ProjectConfigContext);
  const colors = useContext(ThemeContext);
  const location = useLocation();

  const { connectors, connect } = useConnect();
  const { status, chainId } = useAccount();
  const { switchChain, isPending } = useSwitchChain();

  const version = '1.0.0';

  // Navigation items
  const navigationItems = [
    { path: '/', label: 'Vest Portal' },
  ];

  const renderContent = () => {
    if (status === 'connecting') {
      return (
        <div className="flex justify-center items-center min-h-[400px]">
          <Spinner className="animate-spin h-12 w-12" />
        </div>
      );
    }

    if (status === 'connected') {
      if (chainId !== chain.id) {
        return (
          <div className="flex justify-center items-center min-h-[400px]">
            <Card>
              <CardHeader>
                <CardTitle>Switch network</CardTitle>
                <CardDescription>
                  Please switch to the <strong>{chain.name}</strong> network
                </CardDescription>
              </CardHeader>
              <CardContent className="h-full space-y-3">
                <Button
                  variant={'secondary'}
                  disabled={isPending}
                  onClick={() =>
                    switchChain({
                      chainId: chain.id,
                    })
                  }
                >
                  Switch network
                </Button>
              </CardContent>
            </Card>
          </div>
        );
      }

      return (
        <TokensProvider>
          <MembershipsProvider>
            <OldMembershipsProvider>
              <Outlet />
            </OldMembershipsProvider>
          </MembershipsProvider>
        </TokensProvider>
      );
    }

    // Wallet not connected - show connection options
    const _connectors = connectors.filter(
      (connector, index, self) =>
        index === self.findIndex((c) => c.name === connector.name),
    );

    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Card>
          <CardHeader>
            <CardTitle>Connect wallet</CardTitle>
            <CardDescription>Choose a wallet to connect to the application</CardDescription>
          </CardHeader>
          <CardContent className="h-full space-y-3">
            {_connectors.map((connector) => (
              <Button
                key={connector.uid}
                variant={'secondary'}
                onClick={() => {
                  lsSet('wallet', connector.name, 'string');
                  connect({ connector });
                }}
              >
                {connector.name}
              </Button>
            ))}
          </CardContent>
        </Card>
      </div>
    );
  };

  return (
    <div
      className="flex flex-col min-h-screen"
      style={{
        backgroundColor: colors?.background,
        color: colors?.text,
      }}
    >
      <header
        className="sticky top-0 left-0 w-full py-2 z-50"
        style={{ backgroundColor: colors?.header?.background }}
      >
        <div className="container flex items-center justify-between h-14">
          <div className="h-full">
            <Link to="/">
              <img
                src={company.logo}
                alt={company.name}
                width={0}
                height={0}
                sizes="100vw"
                className="object-contain h-full w-auto max-w-36"
              />
            </Link>
          </div>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            {navigationItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`text-sm font-medium transition-colors hover:text-primary ${location.pathname === item.path
                    ? 'text-fuchsia-600'
                    : 'text-purple-900 border-b-2 border-primary'
                  }`}
              >
                {item.label}
              </Link>
            ))}
          </nav>

          {/* Mobile Navigation */}
          <div className="md:hidden">
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="ghost" size="sm">
                  Menu
                </Button>
              </DialogTrigger>
              <DialogContent className="bg-black text-white">
                <DialogHeader>
                  <DialogTitle>Navigation</DialogTitle>
                  <DialogDescription>Choose a page to navigate to</DialogDescription>
                </DialogHeader>
                <div className="space-y-2">
                  {navigationItems.map((item) => (
                    <Link
                      key={item.path}
                      to={item.path}
                      className={`block px-4 py-2 rounded-md text-sm font-medium transition-colors hover:bg-gray-800 ${location.pathname === item.path
                          ? 'bg-primary text-primary-foreground'
                          : 'text-gray-300'
                        }`}
                    >
                      {item.label}
                    </Link>
                  ))}
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </header>

      <main className="relative flex flex-grow">
        <AdminNavigation />
        <div className="container py-6">
          {renderContent()}
        </div>
      </main>

      <footer
        className="py-4 border-t border-white"
        style={{
          backgroundColor: colors?.footer?.background,
          color: colors?.footer?.text,
        }}
      >
        <div className="container flex flex-col items-center gap-1">
          <p>
            {company.name} {new Date().getFullYear()} &copy; All rights reserved
          </p>
          {version && <p className="text-xs">Version {version}</p>}
        </div>
      </footer>
    </div>
  );
}
