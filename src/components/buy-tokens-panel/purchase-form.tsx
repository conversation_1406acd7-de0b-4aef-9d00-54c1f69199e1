import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { getWalletClient } from '@wagmi/core';
import { useContext, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { formatUnits, parseUnits } from 'viem';
import { WagmiContext } from 'wagmi';
import { z } from 'zod';

import { Membership, PresaleRoundState, Round } from '@/class/interface/presale';
import { cutDecimals } from '@/lib/cut-decimals';
import { MembershipsContext } from '@/providers/membership-provider';
import { PresaleContext } from '@/providers/presale-provider';
import { ProjectConfigContext } from '@/providers/project-config-provider';
import { PublicClientContext } from '@/providers/public-client-provider';
import { TokensContext } from '@/providers/tokens-provider';

import { Spinner } from '../icons/spinner';
import { But<PERSON> } from '../ui/button';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from '../ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../ui/form';
import { Input } from '../ui/input';

interface PurchaseFormProps {
  membership: Membership;
  round: Round;
}

export default function PurchaseForm({ membership, round }: PurchaseFormProps) {
  const { fetchMemberships } = useContext(MembershipsContext);
  const { presaleInstance } = useContext(PresaleContext);
  const { chain } = useContext(ProjectConfigContext);
  const config = useContext(WagmiContext);
  const { publicClient } = useContext(PublicClientContext);

  const { vestedToken, collectedToken, fetchCollectedTokenData } =
    useContext(TokensContext);

  const [priceInUnit, setPriceInUnit] = useState('0');
  const [amountInUnit, setAmountInUnit] = useState('0');

  const [needApproval, setNeedApproval] = useState(
    BigInt(vestedToken.allowance || '0') < BigInt(membership.allocation),
  );

  const [isLoading, setIsLoading] = useState(false);

  const buyTokensFormSchema = z.object({
    price: z.custom<string>((value) =>
      inputValidation(
        value,
        formatUnits(BigInt(collectedToken.balance), collectedToken.decimals),
      ),
    ),
    amount: z.custom<string>((value) =>
      inputValidation(
        value,
        formatUnits(BigInt(membership.allocation), vestedToken.decimals),
      ),
    ),
  });

  const form = useForm<z.infer<typeof buyTokensFormSchema>>({
    resolver: zodResolver(buyTokensFormSchema),
    defaultValues: {
      amount: '0',
      price: '0',
    },
  });

  const handlePriceChange = (value: string) => {
    if (!value) {
      form.setValue('price', '');
      return;
    }
    // Replace all non-numeric characters except dot and comma
    value = value.replaceAll(/[^\d.]/g, '').replaceAll(/\.+/g, '.');

    // If value ends with a dot, then it's a decimal number and user is still typing
    if (value.endsWith('.')) {
      form.setValue('price', value);
      return;
    }

    const values = value.split('.');
    let decimalsLength = values[1] ? values[1].length : 0;
    decimalsLength = Math.min(decimalsLength, collectedToken.decimals);

    value =
      values[0] + (decimalsLength > 0 ? '.' + values[1].slice(0, decimalsLength) : '');

    const allowedDecimals = decimalsLength ? values[1].slice(0, decimalsLength) : '';
    const shiftedValue = decimalsLength > 0 ? values[0] + allowedDecimals : value;

    const priceInUnit = parseUnits(
      shiftedValue,
      collectedToken.decimals - decimalsLength,
    );

    const newAmount =
      (BigInt(priceInUnit) * BigInt(10 ** vestedToken.decimals)) /
      BigInt(Number(membership.price));

    setPriceInUnit(priceInUnit.toString());
    setAmountInUnit(newAmount.toString());

    setNeedApproval(BigInt(collectedToken.allowance ?? 0) < priceInUnit);

    form.setValue('price', value);
    form.setValue('amount', formatUnits(newAmount, vestedToken.decimals));
  };

  const handleAmountChange = (value: string) => {
    if (!value) {
      form.setValue('amount', '');
      return;
    }
    // Replace all non-numeric characters except dot and comma
    value = value.replaceAll(/[^\d.]/g, '').replaceAll(/\.+/g, '.');

    // If value ends with a dot, then it's a decimal number and user is still typing
    if (value.endsWith('.')) {
      form.setValue('amount', value);
      return;
    }

    const values = value.split('.');
    let decimalsLength = values[1] ? values[1].length : 0;
    decimalsLength = Math.min(decimalsLength, vestedToken.decimals);

    value =
      values[0] + (decimalsLength > 0 ? '.' + values[1].slice(0, decimalsLength) : '');

    const allowedDecimals = decimalsLength ? values[1].slice(0, decimalsLength) : '';
    const shiftedValue = decimalsLength > 0 ? values[0] + allowedDecimals : value;

    const amountInUnit = parseUnits(shiftedValue, vestedToken.decimals - decimalsLength);

    const newPriceInUnit =
      (BigInt(amountInUnit) * BigInt(membership.price)) /
      BigInt(10 ** (vestedToken.decimals - decimalsLength));

    const newPrice = formatUnits(newPriceInUnit, collectedToken.decimals);

    setAmountInUnit(amountInUnit.toString());
    setPriceInUnit(newPriceInUnit.toString());

    setNeedApproval(BigInt(collectedToken.allowance || 0) < newPriceInUnit);

    form.setValue('amount', value);
    form.setValue('price', newPrice);
  };

  const handleMaxPriceAmount = () => {
    const restOfAllocation = BigInt(membership.allocation) - BigInt(membership.usage.max);

    const allocation =
      BigInt(membership.allocation) > restOfAllocation
        ? restOfAllocation
        : BigInt(membership.allocation);

    const newPriceInUnit =
      (BigInt(allocation) * BigInt(membership.price)) /
      BigInt(10 ** vestedToken.decimals);

    // +1 Szabo just to improve the user experience
    const newPrice = formatUnits(newPriceInUnit, collectedToken.decimals);

    const balanceMinusGas = BigInt(collectedToken.balance);

    handlePriceChange(
      Math.min(
        parseFloat(newPrice),
        parseFloat(formatUnits(balanceMinusGas, collectedToken.decimals)),
      ).toString(),
    );
  };

  async function onSubmit() {
    const client = await getWalletClient(config!);

    setIsLoading(true);

    if (needApproval) {
      try {
        const txHash = await presaleInstance!.setApproval(
          client,
          chain,
          BigInt(priceInUnit),
        );

        await publicClient.waitForTransactionReceipt({
          hash: txHash,
        });

        setNeedApproval(false);
        fetchCollectedTokenData();

        setIsLoading(false);
      } catch (error) {
        console.error('Approval failed:', error);
        // TODO: Add user-friendly error notification
        setIsLoading(false);
      }
    } else {
      // Buy tokens
      try {
        const txHash = await presaleInstance!.buyTokens(
          client,
          chain,
          membership,
          BigInt(amountInUnit),
        );

        await publicClient.waitForTransactionReceipt({
          hash: txHash,
        });

        fetchCollectedTokenData();
        fetchMemberships();
        setIsLoading(false);
      } catch (error) {
        console.error('Purchase failed:', error);
        // TODO: Add user-friendly error notification
        setIsLoading(false);
      }
    }
  }

  useEffect(() => {
    handleMaxPriceAmount();
  }, [membership]);

  return (
    <Card className="col-span-2 row-span-2">
      {/* <p>{membership.id}</p> */}
      <CardHeader>
        <CardTitle>Buy tokens</CardTitle>
        {/* <p>
          Price ({priceInUnit.length}) - {priceInUnit}
        </p>
        <p>
          Amount ({amountInUnit.length}) - {amountInUnit}
  </p> */}
        {/* <p>Allowance - {collectedToken.allowance}</p> */}
      </CardHeader>

      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex flex-col w-full h-full justify-center gap-4"
        >
          <CardContent>
            <FormField
              control={form.control}
              name="price"
              render={({ field }) => (
                <FormItem className="w-full">
                  <div className="flex flex-row justify-between">
                    <FormLabel>Price</FormLabel>

                    <FormDescription>
                      Balance:&nbsp;
                      <span className="font-semibold">
                        {cutDecimals(
                          formatUnits(
                            BigInt(collectedToken.balance),
                            collectedToken.decimals,
                          ),
                          2,
                        )}
                        &nbsp;
                        {collectedToken?.symbol}
                      </span>
                    </FormDescription>
                  </div>

                  <FormControl>
                    <div className="flex flex-row">
                      <Input
                        {...field}
                        onChange={(event) => handlePriceChange(event.target.value)}
                        className="rounded-r-none"
                      />

                      <Button
                        variant={'secondary'}
                        onClick={handleMaxPriceAmount}
                        type="button"
                        className="border-l-0 rounded-l-none"
                      >
                        Max
                      </Button>
                    </div>
                  </FormControl>

                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem className="w-full">
                  <div className="flex flex-row justify-between">
                    <FormLabel>Amount</FormLabel>

                    <FormDescription>
                      Allocation used:&nbsp;
                      <span className="font-semibold">
                        {(() => {
                          return cutDecimals(
                            formatUnits(BigInt(membership.usage.max), vestedToken.decimals),
                            2,
                          );
                        })()}
                        {' / '}
                        {cutDecimals(
                          formatUnits(
                            BigInt(membership.allocation),
                            vestedToken.decimals,
                          ),
                          2,
                        )}
                      </span>
                    </FormDescription>
                  </div>

                  <FormControl>
                    <Input
                      {...field}
                      onChange={(event) => handleAmountChange(event.target.value)}
                    />
                  </FormControl>

                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>

          <CardFooter className="justify-center">
            <Button
              variant={'secondary'}
              type="submit"
              className="gap-2"
              disabled={
                isLoading || (!needApproval && round.state === PresaleRoundState.pending)
              }
            >
              {isLoading && <Spinner className="animate-spin" />}
              {needApproval
                ? `Approve ${collectedToken.symbol}`
                : `BUY ${vestedToken.symbol}`}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
}

export const inputValidation = (value: unknown, max: string) => {
  const castedValue: string = String(value);

  // If value is empty, then it's not valid
  if (castedValue === '') return false;

  // If value ends with a dot, then it's not valid
  if (castedValue.endsWith('.')) return false;

  const floatValue = Number.parseFloat(castedValue);

  // If value is not a number, then it's not valid
  if (Number.isNaN(floatValue)) return false;

  // If value is more than max, then it's not valid
  if (floatValue > Number.parseFloat(max)) return false;

  // If value is less than or equal to 0, then it's not valid
  return !(floatValue <= 0);
};
