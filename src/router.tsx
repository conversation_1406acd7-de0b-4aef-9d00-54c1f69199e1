import { createBrowserRouter, Navigate } from 'react-router-dom';

import AppLayout from './components/app-layout';
import VestApp from './components/vest-app';
import AdminPage from './components/admin-page';

export const router = createBrowserRouter([
  {
    path: '/',
    element: <AppLayout />,
    children: [
      {
        index: true,
        element: <VestApp />,
      },
      {
        path: 'admin',
        element: <AdminPage />,
        
      },
      {
        path: '*',
        element: <Navigate to="/" replace />,
      },
    ],
  },
]);
